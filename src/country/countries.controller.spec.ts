import { Test, TestingModule } from '@nestjs/testing';
import { CountryController } from './country.controller';
import { CountryService } from './country.service';

describe('CountryController', () => {
  let controller: CountryController;
  let service: jest.Mocked<CountryService>;

  beforeEach(async () => {
    const mockCountriesService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CountryController],
      providers: [
        {
          provide: CountryService,
          useValue: mockCountriesService,
        },
      ],
    }).compile();

    controller = module.get<CountryController>(CountryController);
    service = module.get(CountryService);
  });

  describe('create', () => {
    it('should create a new country', async () => {
      const dto = { name: 'Germany' };
      const expected = { id: '1', name: 'Germany' };

      service.create.mockResolvedValue(expected);

      const result = await controller.create(dto);
      expect(result).toEqual(expected);
      expect(service.create).toHaveBeenCalledWith(dto);
    });
  });

  describe('findAll', () => {
    it('should return all countries', async () => {
      const expected = [
        { id: '1', name: 'France', regions: [], offices: [], employees: [] },
        { id: '2', name: 'Spain', regions: [], offices: [], employees: [] },
      ];

      service.findAll.mockResolvedValue(expected);

      const result = await controller.findAll();
      expect(result).toEqual(expected);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a country by ID', async () => {
      const id = '1';
      const expected = { id, name: 'France', regions: [], offices: [], employees: [] };

      service.findOne.mockResolvedValue(expected);

      const result = await controller.findOne(id);
      expect(result).toEqual(expected);
      expect(service.findOne).toHaveBeenCalledWith(id);
    });
  });

  describe('update', () => {
    it('should update a country', async () => {
      const id = '1';
      const dto = { name: 'UpdatedName' };
      const expected = { id, name: 'UpdatedName' };

      service.update.mockResolvedValue(expected);

      const result = await controller.update(id, dto);
      expect(result).toEqual(expected);
      expect(service.update).toHaveBeenCalledWith(id, dto);
    });
  });

  describe('remove', () => {
    it('should delete a country', async () => {
      const id = '1';
      const expected = { id, name: 'DeletedCountry' };

      service.remove.mockResolvedValue(expected);

      const result = await controller.remove(id);
      expect(result).toEqual(expected);
      expect(service.remove).toHaveBeenCalledWith(id);
    });
  });
});
