import { HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { DocumentsController } from './documents.controller';
import { DocumentsService } from './documents.service';
import { CreateDocumentDto } from './dto/create-document.dto';
import { GetDocumentDto } from './dto/get-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';

describe('DocumentsController', () => {
  let controller: DocumentsController;
  let service: DocumentsService;

  const mockDocument: GetDocumentDto = {
    id: '1',
    type: 'PASSPORT',
    fileUrl: 'file1.pdf',
    studentId: 'student1',
    status: 'PENDING',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockService = {
    createDocument: jest.fn(),
    findAllDocumentsByStudentId: jest.fn(),
    findDocumentById: jest.fn(),
    updateDocument: jest.fn(),
    deleteDocument: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DocumentsController],
      providers: [{ provide: DocumentsService, useValue: mockService }],
    }).compile();

    controller = module.get<DocumentsController>(DocumentsController);
    service = module.get<DocumentsService>(DocumentsService);
  });

  afterEach(() => jest.clearAllMocks());

  describe('createDocument', () => {
    it('should create a new document', async () => {
      const dto: CreateDocumentDto = {
        type: 'PASSPORT',
        studentId: 'student1',
      };
      const file = { filename: 'file1.pdf' } as Express.Multer.File;
      mockService.createDocument.mockResolvedValue(mockDocument);

      const result = await controller.createDocument(file, dto);
      expect(result).toEqual(mockDocument);
      expect(mockService.createDocument).toHaveBeenCalledWith(dto, file);
    });

    it('should throw an error if creation fails', async () => {
      const dto: CreateDocumentDto = {
        type: 'PASSPORT',
        studentId: 'student1',
      };
      const file = { filename: 'file1.pdf' } as Express.Multer.File;
      mockService.createDocument.mockRejectedValue(
        new HttpException('Creation failed', HttpStatus.BAD_REQUEST),
      );

      await expect(controller.createDocument(file, dto)).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('findAllDocumentsByStudentId', () => {
    it('should return all documents for a student', async () => {
      mockService.findAllDocumentsByStudentId.mockResolvedValue([mockDocument]);

      const mockPageOptions = { page: 1, take: 10, skip: 0 };
      const result = await controller.findAllDocumentsByStudentId(mockPageOptions, 'student1');
      expect(result).toEqual([mockDocument]);
      expect(mockService.findAllDocumentsByStudentId).toHaveBeenCalledWith(
        'student1',
        mockPageOptions,
      );
    });

    it('should throw an error if retrieval fails', async () => {
      mockService.findAllDocumentsByStudentId.mockRejectedValue(
        new HttpException('Retrieval failed', HttpStatus.NOT_FOUND),
      );

      const mockPageOptions = { page: 1, take: 10, skip: 0 };
      await expect(
        controller.findAllDocumentsByStudentId(mockPageOptions, 'student1'),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('findDocumentById', () => {
    it('should return a document by ID', async () => {
      mockService.findDocumentById.mockResolvedValue(mockDocument);

      const result = await controller.findDocumentById('1');
      expect(result).toEqual(mockDocument);
      expect(mockService.findDocumentById).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException if document not found', async () => {
      mockService.findDocumentById.mockRejectedValue(
        new NotFoundException('Document not found'),
      );

      await expect(controller.findDocumentById('1')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('updateDocument', () => {
    it('should update an existing document', async () => {
      const dto: UpdateDocumentDto = {
        type: 'PASSPORT',
        studentId: 'student1',
      };
      const file = { filename: 'file2.pdf' } as Express.Multer.File;
      mockService.updateDocument.mockResolvedValue(mockDocument);

      const result = await controller.updateDocument('1', file, dto);
      expect(result).toEqual(mockDocument);
      expect(mockService.updateDocument).toHaveBeenCalledWith('1', dto, file);
    });

    it('should throw NotFoundException if document not found', async () => {
      const dto: UpdateDocumentDto = {
        type: 'PASSPORT',
        studentId: 'student1',
      };
      mockService.updateDocument.mockRejectedValue(
        new NotFoundException('Document not found'),
      );

      await expect(controller.updateDocument('1', null, dto)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('deleteDocument', () => {
    it('should delete a document', async () => {
      mockService.deleteDocument.mockResolvedValue({
        message: 'Document deleted successfully',
      });

      const result = await controller.deleteDocument('1');
      expect(result).toEqual({ message: 'Document deleted successfully' });
      expect(mockService.deleteDocument).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException if document not found', async () => {
      mockService.deleteDocument.mockRejectedValue(
        new NotFoundException('Document not found'),
      );

      await expect(controller.deleteDocument('1')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
