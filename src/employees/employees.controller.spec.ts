import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { CreateAnEmployeeDto } from './dto/create-an-employee.dto';
import { GetAnEmployeeDto } from './dto/get-an-employee.dto';
import { UpdateAnEmployeeDto } from './dto/update-an-employee.dto';
import { EmployeesController } from './employees.controller';
import { EmployeesService } from './employees.service';

describe('EmployeesController', () => {
  let controller: EmployeesController;
  let service: EmployeesService;

  const mockEmployee: GetAnEmployeeDto = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    phone: '**********',
    officeId: '123',
    regionId: '456',
    students: [],
    user: {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'ACTIVE',
    },
  };

  const mockService = {
    createAnEmployee: jest.fn().mockResolvedValue(mockEmployee),
    getAllEmployees: jest.fn().mockResolvedValue([mockEmployee]),
    getEmployeeById: jest.fn().mockResolvedValue(mockEmployee),
    updateAnEmployee: jest.fn().mockResolvedValue(mockEmployee),
    deleteAnEmployee: jest.fn().mockResolvedValue(mockEmployee),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EmployeesController],
      providers: [{ provide: EmployeesService, useValue: mockService }],
    }).compile();

    controller = module.get<EmployeesController>(EmployeesController);
    service = module.get<EmployeesService>(EmployeesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createAnEmployee', () => {
    it('should create an employee', async () => {
      const dto: CreateAnEmployeeDto = {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        phone: '**********',
        password: 'password',
      };
      await expect(controller.createAnEmployee(dto)).resolves.toEqual(
        mockEmployee,
      );
      expect(service.createAnEmployee).toHaveBeenCalledWith(dto);
    });
  });

  describe('getAllEmployees', () => {
    it('should return all employees', async () => {
      await expect(controller.getAllEmployees()).resolves.toEqual([
        mockEmployee,
      ]);
      expect(service.getAllEmployees).toHaveBeenCalled();
    });
  });

  describe('getEmployeeById', () => {
    it('should return an employee by ID', async () => {
      await expect(controller.getEmployeeById('1')).resolves.toEqual(
        mockEmployee,
      );
      expect(service.getEmployeeById).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException if employee not found', async () => {
      jest
        .spyOn(service, 'getEmployeeById')
        .mockRejectedValue(new NotFoundException());
      await expect(controller.getEmployeeById('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('updateAnEmployee', () => {
    it('should update an employee', async () => {
      const dto: UpdateAnEmployeeDto = {
        firstName: 'Updated',
        lastName: 'Name',
        phone: '0987654321',

      };
      await expect(controller.updateAnEmployee('1', dto)).resolves.toEqual(
        mockEmployee,
      );
      expect(service.updateAnEmployee).toHaveBeenCalledWith('1', dto);
    });
  });

  describe('deleteAnEmployee', () => {
    it('should delete an employee', async () => {
      await expect(controller.deleteAnEmployee('1')).resolves.toEqual(
        mockEmployee,
      );
      expect(service.deleteAnEmployee).toHaveBeenCalledWith('1');
    });
  });
});
