import { ConflictException, HttpException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import * as bcrypt from 'bcrypt';
import { PrismaService } from 'src/prisma/prisma.service';
import { EmployeesService } from './employees.service';

describe('EmployeesService', () => {
  let service: EmployeesService;
  let prisma: jest.Mocked<PrismaService>;

  const prismaMock = {
    employee: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    user: {
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeesService,
        { provide: PrismaService, useValue: prismaMock },
      ],
    }).compile();

    service = module.get<EmployeesService>(EmployeesService);
    prisma = module.get<PrismaService>(
      PrismaService,
    ) as jest.Mocked<PrismaService>;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createAnEmployee', () => {
    it('should successfully create an employee', async () => {
      const dto = {
        email: '<EMAIL>',
        name: 'John Doe',
        password: 'password123',
      };
      const hashedPassword = await bcrypt.hash(dto.password, 10);
      const createdEmployee = {
        id: '1',
        user: {
          id: '1',
          email: dto.email,
          name: dto.name,
          password: hashedPassword,
        },
      };

      prismaMock.employee.create.mockResolvedValue(createdEmployee);

      await expect(service.createAnEmployee(dto)).resolves.toEqual(
        createdEmployee,
      );
      expect(prismaMock.employee.create).toHaveBeenCalled();
    });

    it('should throw ConflictException if email already exists', async () => {
      prismaMock.employee.create.mockRejectedValue(
        new PrismaClientKnownRequestError('Unique constraint failed', {
          code: 'P2002',
          clientVersion: '4.10.0',
        }),
      );

      await expect(
        service.createAnEmployee({
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          phone: '1234567890',
          password: 'password123',
        }),
      ).rejects.toThrow(ConflictException);
    });

    it('should throw HttpException on other errors', async () => {
      prismaMock.employee.create.mockRejectedValue(new Error('Database error'));
      await expect(
        service.createAnEmployee({
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          phone: '1234567890',
          password: 'password123',
        }),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getAllEmployees', () => {
    it('should return a list of employees', async () => {
      const employees = [
        {
          id: '1',
          user: { id: '1', name: 'John Doe', email: '<EMAIL>' },
        },
      ];
      prismaMock.employee.findMany.mockResolvedValue(employees);

      await expect(service.getAllEmployees()).resolves.toEqual(employees);
    });

    it('should throw HttpException on failure', async () => {
      prismaMock.employee.findMany.mockRejectedValue(
        new Error('Database error'),
      );
      await expect(service.getAllEmployees()).rejects.toThrow(HttpException);
    });
  });

  describe('getEmployeeById', () => {
    it('should return an employee by ID', async () => {
      const employee = { id: '1', user: { id: '1', name: 'John Doe' } };
      prismaMock.employee.findUnique.mockResolvedValue(employee);

      await expect(service.getEmployeeById('1')).resolves.toEqual(employee);
    });

    it('should throw HttpException if employee not found', async () => {
      prismaMock.employee.findUnique.mockResolvedValue(null);
      await expect(service.getEmployeeById('1')).rejects.toThrow(HttpException);
    });
  });

  describe('updateAnEmployee', () => {
    it('should update an employee', async () => {
      const updatedEmployee = {
        id: '1',
        user: { id: '1', name: 'Updated Name' },
      };
      prismaMock.employee.findUnique.mockResolvedValue(updatedEmployee);
      prismaMock.employee.update.mockResolvedValue(updatedEmployee);

      await expect(
        service.updateAnEmployee('1', { firstName: 'Updated', lastName: 'Name', phone: '0987654321' }),
      ).resolves.toEqual(updatedEmployee);
    });

    it('should throw HttpException if employee not found', async () => {
      prismaMock.employee.findUnique.mockResolvedValue(null);
      await expect(
        service.updateAnEmployee('1', { firstName: 'Updated', lastName: 'Name', phone: '0987654321' }),
      ).rejects.toThrow(HttpException);
    });
  });

  describe('deleteAnEmployee', () => {
    it('should delete an employee and return it', async () => {
      const employee = { id: '1', user: { id: '1' } };
      prismaMock.employee.findUnique.mockResolvedValue(employee);
      prismaMock.employee.delete.mockResolvedValue(employee);
      prismaMock.user.delete.mockResolvedValue(employee.user);

      await expect(service.deleteAnEmployee('1')).resolves.toEqual(employee);
    });

    it('should throw HttpException if employee not found', async () => {
      prismaMock.employee.findUnique.mockResolvedValue(null);
      await expect(service.deleteAnEmployee('1')).rejects.toThrow(
        HttpException,
      );
    });
  });
});
