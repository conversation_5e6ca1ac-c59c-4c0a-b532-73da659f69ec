import { Test, TestingModule } from '@nestjs/testing';
import { WorkflowsController } from './workflows.controller';

describe('WorkflowsController', () => {
  let controller: WorkflowsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkflowsController],
      providers: [
        {
          provide: 'WorkflowsService',
          useValue: {
            createWorkflow: jest.fn(),
            getAllWorkflows: jest.fn(),
            getWorkflowById: jest.fn(),
            updateWorkflow: jest.fn(),
            deleteWorkflow: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<WorkflowsController>(WorkflowsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
