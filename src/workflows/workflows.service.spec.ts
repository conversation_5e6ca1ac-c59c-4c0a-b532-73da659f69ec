import { Test, TestingModule } from '@nestjs/testing';
import { WorkflowsService } from './workflows.service';
import { PrismaService } from '../prisma/prisma.service';

describe('WorkflowsService', () => {
  let service: WorkflowsService;

  const mockPrismaService = {
    workflowStep: {
      createMany: jest.fn(),
    },
    workflow: {
      findUnique: jest.fn().mockResolvedValue({
        id: 'workflow-1',
        steps: [],
      }),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkflowsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<WorkflowsService>(WorkflowsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create workflow successfully', async () => {
    const createWorkflowDto = {
      name: 'Test Workflow',
      description: 'Test Description',
      active: true,
      steps: [],
      triggers: []
    };

    const expectedWorkflow = {
      id: 'workflow-1',
      name: 'Test Workflow',
      description: 'Test Description',
      active: true,
      steps: [],
      triggers: []
    };

    jest.spyOn(service, 'createWorkflow').mockResolvedValue(expectedWorkflow);

    const result = await service.createWorkflow(createWorkflowDto);
    expect(result).toEqual(expectedWorkflow);
  });
});
