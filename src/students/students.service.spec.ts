import { ConflictException, HttpException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import * as bcrypt from 'bcrypt';
import { PrismaService } from '../prisma/prisma.service';
import { AddAStudentDto } from './dto/add-a-student.dto';
import { AddStudentLeadDto } from './dto/add-student-lead.dto';
import { GetAStudentDto } from './dto/get-a-student.dto';
import { UpdateAStudentDto } from './dto/update-a-student.dto';
import { StudentsService } from './students.service';

jest.mock('bcrypt', () => ({
  hash: jest.fn().mockResolvedValue('hashedPassword'),
}));

describe('StudentsService', () => {
  let service: StudentsService;
  let prisma: PrismaService;

  const mockStudent: GetAStudentDto = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    dateOfBirth: new Date('1995-01-01'),
    cellNo: '**********',
    phoneNo: '**********',
    passport: true,
    city: 'New York',
    leadSource: 'SOCIAL_MEDIA',
    leadSourceName: 'Facebook',
    status: 'ACTIVE',
    subStatus: 'EMAIL_SENT',
    lastInstituteAttended: 'FAST-NUCES',
    lastInstituteDegree: 'BSCS',
    interestedDegreeLevel: 'FOUNDATION',
    interestedFields: ['Computer Science', 'Mathematics', 'Physics'],
    interestedCountries: ['USA', 'UK'],
    visas: [],
    qualifications: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    user: {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      status: 'ACTIVE',
    },
  };

  const mockPrisma = {
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    student: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StudentsService,
        { provide: PrismaService, useValue: mockPrisma },
      ],
    }).compile();

    service = module.get<StudentsService>(StudentsService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => jest.clearAllMocks());

  describe('addNewLead', () => {
    it('should create a new lead with generated password', async () => {
      const dto: AddStudentLeadDto = {
        email: '<EMAIL>',
        firstName: 'Lead',
        lastName: 'Test',
        dateOfBirth: new Date('1995-01-01'),
        cellNo: '**********',
        phoneNo: '**********',
        passport: true,
        city: 'Test City',
        leadSource: 'SOCIAL_MEDIA',
        leadSourceName: 'Facebook',
        status: 'ACTIVE',
        subStatus: 'EMAIL_SENT',
        lastInstituteAttended: 'Test Institute',
        lastInstituteDegree: 'Test Degree',
        interestedDegreeLevel: 'FOUNDATION',
        interestedFields: ['Test Field'],
        interestedCountries: ['Test Country'],
        qualifications: [],
      };
      mockPrisma.user.findUnique.mockResolvedValue(null);
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        const tx = {
          user: { create: jest.fn().mockResolvedValue({ id: 'user1' }) },
          student: { create: jest.fn().mockResolvedValue(mockStudent) },
        };
        return callback(tx);
      });

      const result = await service.addNewLead(dto);
      expect(result).toEqual(mockStudent);
      expect(bcrypt.hash).toHaveBeenCalled();
    });

    it('should throw ConflictException for existing email', async () => {
      const dto: AddStudentLeadDto = {
        email: '<EMAIL>',
        firstName: 'Lead',
        lastName: 'Test',
        dateOfBirth: new Date('1995-01-01'),
        cellNo: '**********',
        phoneNo: '**********',
        passport: true,
        city: 'Test City',
        leadSource: 'SOCIAL_MEDIA',
        leadSourceName: 'Facebook',
        status: 'ACTIVE',
        subStatus: 'EMAIL_SENT',
        lastInstituteAttended: 'Test Institute',
        lastInstituteDegree: 'Test Degree',
        interestedDegreeLevel: 'FOUNDATION',
        interestedFields: ['Test Field'],
        interestedCountries: ['Test Country'],
        qualifications: [],
      };
      mockPrisma.user.findUnique.mockResolvedValue({ id: 'existing' });

      await expect(service.addNewLead(dto)).rejects.toThrow(ConflictException);
    });
  });

  describe('createAStudent', () => {
    it('should create student with hashed password', async () => {
      const dto: AddAStudentDto = {
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'Student',
        password: 'password',
        dateOfBirth: new Date('1995-01-01'),
        cellNo: '**********',
        phoneNo: '**********',
        passport: true,
        city: 'Test City',
        leadSource: 'SOCIAL_MEDIA',
        leadSourceName: 'Facebook',
        status: 'ACTIVE',
        subStatus: 'EMAIL_SENT',
        lastInstituteAttended: 'Test Institute',
        lastInstituteDegree: 'Test Degree',
        interestedDegreeLevel: 'FOUNDATION',
        interestedFields: ['Test Field'],
        interestedCountries: ['Test Country'],
        qualifications: [],
      };
      mockPrisma.user.findUnique.mockResolvedValue(null);
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        const tx = {
          user: { create: jest.fn().mockResolvedValue({ id: 'user1' }) },
          student: { create: jest.fn().mockResolvedValue(mockStudent) },
        };
        return callback(tx);
      });

      const result = await service.createAStudent(dto);
      expect(result).toEqual(mockStudent);
      expect(bcrypt.hash).toHaveBeenCalledWith(dto.password, 10);
    });

    it('should throw ConflictException for existing email', async () => {
      const dto: AddStudentLeadDto = {
        email: '<EMAIL>',
        firstName: 'Lead',
        lastName: 'Test',
        dateOfBirth: new Date('1995-01-01'),
        cellNo: '**********',
        phoneNo: '**********',
        passport: true,
        city: 'Test City',
        leadSource: 'SOCIAL_MEDIA',
        leadSourceName: 'Facebook',
        status: 'ACTIVE',
        subStatus: 'EMAIL_SENT',
        lastInstituteAttended: 'Test Institute',
        lastInstituteDegree: 'Test Degree',
        interestedDegreeLevel: 'FOUNDATION',
        interestedFields: ['Test Field'],
        interestedCountries: ['Test Country'],
        qualifications: [],
      };
      mockPrisma.user.findUnique.mockResolvedValue({ id: 'existing' });

      await expect(service.addNewLead(dto)).rejects.toThrow(ConflictException);
    });
  });

  describe('getStudentById', () => {
    it('should return student if exists', async () => {
      mockPrisma.student.findUnique.mockResolvedValue(mockStudent);

      expect(await service.getStudentById('1')).toEqual(mockStudent);
    });

    it('should throw NotFound if student not found', async () => {
      mockPrisma.student.findUnique.mockResolvedValue(null);

      await expect(service.getStudentById('999')).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('updateAStudent', () => {
    it('should update existing student', async () => {
      const dto: UpdateAStudentDto = {
        firstName: 'Updated',
        leadSourceName: 'Facebook',
        subStatus: 'EMAIL_SENT'
      };
      mockPrisma.student.findUnique.mockResolvedValue(mockStudent);
      mockPrisma.student.update.mockResolvedValue(mockStudent);

      expect(await service.updateAStudent('1', dto)).toEqual(mockStudent);
    });

    it('should throw NotFound if student not found', async () => {
      mockPrisma.student.findUnique.mockResolvedValue(null);

      await expect(service.getStudentById('999')).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('deleteAStudent', () => {
    it('should mark user as deleted', async () => {
      mockPrisma.student.findUnique.mockResolvedValue(mockStudent);
      mockPrisma.user.update.mockResolvedValue({
        ...mockStudent.user,
        status: 'DELETED',
      });

      const result = await service.deleteAStudent('1');
      expect(result).toEqual(mockStudent);
      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user1' },
        data: { status: 'DELETED' },
      });
    });

    it('should throw if student already deleted', async () => {
      const deletedStudent = {
        ...mockStudent,
        user: { ...mockStudent.user, status: 'DELETED' },
      };
      mockPrisma.student.findUnique.mockResolvedValue(deletedStudent);

      await expect(service.deleteAStudent('1')).rejects.toThrow(HttpException);
    });

    it('should throw NotFound if student not found', async () => {
      mockPrisma.student.findUnique.mockResolvedValue(null);

      await expect(service.getStudentById('999')).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('error handling', () => {
    it('should handle Prisma unique constraint error', async () => {
      const prismaError = new PrismaClientKnownRequestError('message', {
        code: 'P2002',
        clientVersion: '1',
      });

      mockPrisma.user.findUnique.mockRejectedValue(prismaError);

      await expect(
        service.createAStudent({} as AddAStudentDto),
      ).rejects.toThrow(ConflictException);
    });
  });
});
