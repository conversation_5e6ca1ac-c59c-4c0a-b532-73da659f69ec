import { ConflictException, HttpException, HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AddAStudentDto } from './dto/add-a-student.dto';
import { AddStudentLeadDto } from './dto/add-student-lead.dto';
import { GetAStudentDto } from './dto/get-a-student.dto';
import { UpdateAStudentDto } from './dto/update-a-student.dto';
import { StudentsController } from './students.controller';
import { StudentsService } from './students.service';

describe('StudentsController', () => {
  let controller: StudentsController;
  let service: StudentsService;

  const mockStudent: GetAStudentDto = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    dateOfBirth: new Date('1995-01-01'),
    cellNo: '1234567890',
    phoneNo: '1234567890',
    passport: true,
    city: 'New York',
    leadSource: 'SOCIAL_MEDIA',
    leadSourceName: 'Facebook',
    status: 'ACTIVE',
    subStatus: 'EMAIL_SENT',
    lastInstituteAttended: 'FAST-NUCES',
    lastInstituteDegree: 'BSCS',
    interestedDegreeLevel: 'FOUNDATION',
    interestedFields: ['Computer Science', 'Mathematics', 'Physics'],
    interestedCountries: ['USA', 'UK'],
    visas: [],
    qualifications: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    user: {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      status: 'ACTIVE',
    },
  };

  const mockService = {
    addNewLead: jest.fn(),
    createAStudent: jest.fn(),
    getAllStudents: jest.fn(),
    getStudentById: jest.fn(),
    updateAStudent: jest.fn(),
    deleteAStudent: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StudentsController],
      providers: [{ provide: StudentsService, useValue: mockService }],
    }).compile();

    controller = module.get<StudentsController>(StudentsController);
    service = module.get<StudentsService>(StudentsService);
  });

  afterEach(() => jest.clearAllMocks());

  describe('addNewLead', () => {
    it('should successfully add a new lead', async () => {
      const dto: AddStudentLeadDto = {
        email: '<EMAIL>',
        firstName: 'Lead',
        lastName: 'New',
        dateOfBirth: new Date('1995-01-01'),
        cellNo: '*********',
        phoneNo: '*********',
        passport: true,
        city: 'lahore',
        leadSource: 'SOCIAL_MEDIA',
        leadSourceName: 'Facebook',
        status: 'ACTIVE',
        subStatus: 'EMAIL_SENT',
        lastInstituteAttended: 'FAST-NUCES',
        lastInstituteDegree: 'BSCS',
        interestedDegreeLevel: 'FOUNDATION',
        interestedFields: ['Computer Science', 'Mathematics', 'Physics'],
        interestedCountries: ['USA', 'UK'],
        qualifications: [],
      };
      mockService.addNewLead.mockResolvedValue({ id: '1', ...dto });

      expect(await controller.addNewLead(dto)).toEqual({ ...dto, id: '1' });
      expect(service.addNewLead).toHaveBeenCalledWith(dto);
    });

    it('should throw ConflictException on duplicate email', async () => {
      const dto: AddStudentLeadDto = {
        email: '<EMAIL>',
        firstName: 'Lead',
        lastName: 'Test',
        dateOfBirth: new Date('1995-01-01'),
        cellNo: '1234567890',
        phoneNo: '1234567890',
        passport: true,
        city: 'Test City',
        leadSource: 'SOCIAL_MEDIA',
        leadSourceName: 'Facebook',
        status: 'ACTIVE',
        subStatus: 'EMAIL_SENT',
        lastInstituteAttended: 'Test Institute',
        lastInstituteDegree: 'Test Degree',
        interestedDegreeLevel: 'FOUNDATION',
        interestedFields: ['Test Field'],
        interestedCountries: ['Test Country'],
        qualifications: [],
      };
      mockService.addNewLead.mockRejectedValue(
        new ConflictException('Email exists'),
      );

      await expect(controller.addNewLead(dto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('createAStudent', () => {
    it('should create a new student', async () => {
      const dto: AddAStudentDto = {
        email: '<EMAIL>',
        firstName: 'Student',
        password: 'password',
        lastName: 'New',
        dateOfBirth: new Date('1995-01-01'),
        cellNo: '*********',
        phoneNo: '*********',
        passport: true,
        city: 'lahore',
        leadSource: 'SOCIAL_MEDIA',
        leadSourceName: 'Facebook',
        status: 'ACTIVE',
        subStatus: 'EMAIL_SENT',
        lastInstituteAttended: 'FAST-NUCES',
        lastInstituteDegree: 'BSCS',
        interestedDegreeLevel: 'FOUNDATION',
        interestedFields: ['Computer Science', 'Mathematics', 'Physics'],
        interestedCountries: ['USA', 'UK'],
        qualifications: [],
      };
      mockService.createAStudent.mockResolvedValue({ ...dto, id: '1' });

      expect(await controller.createAStudent(dto)).toEqual({ ...dto, id: '1' });
      expect(service.createAStudent).toHaveBeenCalledWith(dto);
    });

    it('should propagate service errors', async () => {
      const dto: AddAStudentDto = {
        email: '<EMAIL>',
        firstName: 'Error',
        password: 'pass',
        lastName: 'Test',
        dateOfBirth: new Date('1995-01-01'),
        cellNo: '1234567890',
        phoneNo: '1234567890',
        passport: true,
        city: 'Test City',
        leadSource: 'SOCIAL_MEDIA',
        leadSourceName: 'Facebook',
        status: 'ACTIVE',
        subStatus: 'EMAIL_SENT',
        lastInstituteAttended: 'Test Institute',
        lastInstituteDegree: 'Test Degree',
        interestedDegreeLevel: 'FOUNDATION',
        interestedFields: ['Test Field'],
        interestedCountries: ['Test Country'],
        qualifications: [],
      };
      mockService.createAStudent.mockRejectedValue(
        new ConflictException('Email exists'),
      );

      await expect(controller.createAStudent(dto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('getAllStudents', () => {
    it('should return array of students', async () => {
      const mockQuery = {};
      const mockPageOptions = { page: 1, take: 10, skip: 0 };
      const mockResult = {
        data: [mockStudent, mockStudent],
        meta: { page: 1, take: 10, itemCount: 2, pageCount: 1, hasPreviousPage: false, hasNextPage: false }
      };
      mockService.getAllStudents.mockResolvedValue(mockResult);

      expect(await controller.getAllStudents(mockQuery, mockPageOptions)).toEqual(mockResult);
      expect(service.getAllStudents).toHaveBeenCalledWith(mockQuery, mockPageOptions, undefined);
    });
  });

  describe('getStudentById', () => {
    it('should return a single student', async () => {
      mockService.getStudentById.mockResolvedValue(mockStudent);

      expect(await controller.getStudentById('1')).toEqual(mockStudent);
      expect(service.getStudentById).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException', async () => {
      mockService.getStudentById.mockRejectedValue(
        new HttpException('Not found', HttpStatus.NOT_FOUND),
      );

      await expect(controller.getStudentById('999')).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('updateAStudent', () => {
    it('should update a student', async () => {
      const dto: UpdateAStudentDto = {
        firstName: 'Updated',
        leadSourceName: 'Facebook',
        subStatus: 'EMAIL_SENT'
      };
      mockService.updateAStudent.mockResolvedValue(mockStudent);

      expect(await controller.updateAStudent('1', dto)).toEqual(mockStudent);
      expect(service.updateAStudent).toHaveBeenCalledWith('1', dto);
    });

    it('should throw NotFoundException', async () => {
      mockService.getStudentById.mockRejectedValue(
        new HttpException('Not found', HttpStatus.NOT_FOUND),
      );

      await expect(controller.getStudentById('999')).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('deleteAStudent', () => {
    it('should delete a student', async () => {
      mockService.deleteAStudent.mockResolvedValue(mockStudent);

      expect(await controller.deleteAStudent('1')).toEqual(mockStudent);
      expect(service.deleteAStudent).toHaveBeenCalledWith('1');
    });

    it('should throw NotFoundException', async () => {
      mockService.getStudentById.mockRejectedValue(
        new HttpException('Not found', HttpStatus.NOT_FOUND),
      );

      await expect(controller.getStudentById('999')).rejects.toThrow(
        HttpException,
      );
    });
  });
});
